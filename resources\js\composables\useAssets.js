
export function useAssets() {
  
  const getAssetUrl = (path) => {

    const baseUrl = window.APP_PUBLIC_PATH || '/rmscrmseries/public';
    const cleanPath = path.startsWith('/') ? path.slice(1) : path;

    console.log('Asset URL Debug:', {
      path: path,
      cleanPath: cleanPath,
      APP_URL: window.APP_URL,
      APP_PUBLIC_PATH: window.APP_PUBLIC_PATH,
      baseUrl: baseUrl,
      finalUrl: `${baseUrl}/${cleanPath}`
    });
    if (!baseUrl) {
      console.warn('Neither APP_URL nor APP_PUBLIC_PATH is set. Using relative path.');
      return `/${cleanPath}`;
    }

    return `${baseUrl}/${cleanPath}`;
  };

  return {
    getAssetUrl
  };
}
